#!/usr/bin/env python3
"""
🗂️ وكيل التنظيم النهائي - نظام كريستال دايموند
Final Organization Agent - Crestal Diamond System
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class FinalOrganizerAgent:
    """وكيل التنظيم النهائي للملفات"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.agent_name = "Final Organizer Agent"
        self.log_file = self.project_root / "agents" / "final_organizer.log"
        
    def log_action(self, action, details=""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"🗂️ [{timestamp}] {self.agent_name}: {action}"
        if details:
            log_entry += f" - {details}"
        
        print(log_entry)
        
        # كتابة في ملف السجل
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
    
    def verify_final_organization(self):
        """التحقق من التنظيم النهائي"""
        self.log_action("بدء التحقق من التنظيم النهائي")
        
        verification_report = {
            "timestamp": datetime.now().isoformat(),
            "core_files_in_root": [],
            "organized_directories": {},
            "misplaced_files": [],
            "empty_directories": [],
            "temp_files": [],
            "status": "unknown"
        }
        
        # فحص الملفات الأساسية في الجذر
        core_files = ["app.py", "requirements.txt", "README.md", "run_app.bat"]
        for file in core_files:
            file_path = self.project_root / file
            if file_path.exists():
                verification_report["core_files_in_root"].append(file)
        
        # فحص المجلدات المنظمة
        organized_dirs = ["docs", "pages", "database", "agents", "Archive", "Collaborative_Workspace"]
        for directory in organized_dirs:
            dir_path = self.project_root / directory
            if dir_path.exists():
                files = [f.name for f in dir_path.iterdir() if f.is_file()]
                verification_report["organized_directories"][directory] = {
                    "file_count": len(files),
                    "files": files[:10]  # أول 10 ملفات فقط
                }
        
        # البحث عن ملفات في غير مكانها
        for item in self.project_root.iterdir():
            if item.is_file() and item.name not in core_files:
                # تجاهل ملفات النظام
                if not item.name.startswith('.') and item.suffix not in ['.pyc']:
                    verification_report["misplaced_files"].append(item.name)
        
        # البحث عن مجلدات فارغة
        for item in self.project_root.iterdir():
            if item.is_dir() and item.name not in ["__pycache__", ".git", "crestal_diamond_env"]:
                try:
                    if not any(item.iterdir()):
                        verification_report["empty_directories"].append(item.name)
                except:
                    pass
        
        # البحث عن ملفات مؤقتة
        temp_patterns = ["*.pyc", "*.tmp", "*.temp", "*~"]
        for pattern in temp_patterns:
            for file_path in self.project_root.rglob(pattern):
                verification_report["temp_files"].append(str(file_path.relative_to(self.project_root)))
        
        # تحديد الحالة العامة
        if (len(verification_report["core_files_in_root"]) == 4 and 
            len(verification_report["misplaced_files"]) == 0 and
            len(verification_report["organized_directories"]) >= 5):
            verification_report["status"] = "excellent"
        elif len(verification_report["misplaced_files"]) <= 2:
            verification_report["status"] = "good"
        else:
            verification_report["status"] = "needs_work"
        
        self.log_action("تم التحقق من التنظيم", 
                       f"الحالة: {verification_report['status']}, "
                       f"ملفات في غير مكانها: {len(verification_report['misplaced_files'])}")
        
        return verification_report
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي للتنظيم"""
        verification = self.verify_final_organization()
        
        report_lines = [
            "🗂️ التقرير النهائي للتنظيم - نظام كريستال دايموند",
            "=" * 60,
            f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"🤖 الوكيل: {self.agent_name}",
            f"📊 الحالة العامة: {verification['status']}",
            "",
            "📋 الملفات الأساسية في الجذر:",
        ]
        
        for file in verification["core_files_in_root"]:
            report_lines.append(f"   ✅ {file}")
        
        report_lines.extend([
            "",
            "📁 المجلدات المنظمة:",
        ])
        
        for directory, info in verification["organized_directories"].items():
            report_lines.append(f"   📂 {directory}: {info['file_count']} ملف")
        
        if verification["misplaced_files"]:
            report_lines.extend([
                "",
                "⚠️ ملفات في غير مكانها:",
            ])
            for file in verification["misplaced_files"]:
                report_lines.append(f"   - {file}")
        
        if verification["empty_directories"]:
            report_lines.extend([
                "",
                "🗑️ مجلدات فارغة:",
            ])
            for directory in verification["empty_directories"]:
                report_lines.append(f"   - {directory}")
        
        if verification["temp_files"]:
            report_lines.extend([
                "",
                f"🧹 ملفات مؤقتة ({len(verification['temp_files'])}):",
            ])
            for file in verification["temp_files"][:5]:  # أول 5 ملفات فقط
                report_lines.append(f"   - {file}")
            if len(verification["temp_files"]) > 5:
                report_lines.append(f"   ... و {len(verification['temp_files']) - 5} ملف آخر")
        
        # تقييم الحالة
        status_messages = {
            "excellent": "🎉 ممتاز! المشروع منظم بشكل مثالي",
            "good": "✅ جيد! تنظيم جيد مع بعض التحسينات البسيطة",
            "needs_work": "⚠️ يحتاج عمل! هناك ملفات تحتاج إعادة تنظيم"
        }
        
        report_lines.extend([
            "",
            "🎯 التقييم النهائي:",
            f"   {status_messages.get(verification['status'], 'غير معروف')}",
            "",
            "📊 الإحصائيات:",
            f"   📄 الملفات الأساسية: {len(verification['core_files_in_root'])}/4",
            f"   📁 المجلدات المنظمة: {len(verification['organized_directories'])}",
            f"   ⚠️ ملفات في غير مكانها: {len(verification['misplaced_files'])}",
            f"   🗑️ مجلدات فارغة: {len(verification['empty_directories'])}",
            f"   🧹 ملفات مؤقتة: {len(verification['temp_files'])}",
            "",
            "📋 الهيكل النهائي المثالي:",
            "Crestal Diamond/",
            "├── app.py (الملف الرئيسي)",
            "├── requirements.txt (التبعيات)",
            "├── README.md (الوثائق الرئيسية)",
            "├── run_app.bat (ملف التشغيل)",
            "├── pages/ (صفحات النظام)",
            "├── database/ (قاعدة البيانات)",
            "├── agents/ (الوكلاء الأذكياء)",
            "├── docs/ (الوثائق والتقارير)",
            "├── Archive/ (الأرشيف)",
            "├── temp/ (الملفات المؤقتة)",
            "├── backup/ (النسخ الاحتياطية)",
            "└── Collaborative_Workspace/ (مساحة العمل التعاونية)",
            "",
            "=" * 60,
            "✅ تم إكمال التحقق من التنظيم النهائي"
        ])
        
        # حفظ التقرير
        report_file = self.project_root / "docs" / "FINAL_ORGANIZATION_VERIFICATION.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        self.log_action("تم إنشاء التقرير النهائي", str(report_file))
        
        # طباعة التقرير
        print("\n" + "\n".join(report_lines))
        
        return verification

def main():
    """الدالة الرئيسية"""
    agent = FinalOrganizerAgent()
    
    print("🗂️ === وكيل التنظيم النهائي ===")
    print("=" * 50)
    
    # التحقق من التنظيم وإنشاء التقرير
    verification = agent.generate_final_report()
    
    print(f"\n🎯 الحالة النهائية: {verification['status']}")
    print(f"📊 ملفات في غير مكانها: {len(verification['misplaced_files'])}")
    print(f"📁 مجلدات منظمة: {len(verification['organized_directories'])}")

if __name__ == "__main__":
    main()
