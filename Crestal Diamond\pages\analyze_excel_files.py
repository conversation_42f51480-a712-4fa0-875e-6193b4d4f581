#!/usr/bin/env python3
"""
📊 تحليل ملفات الإكسل - نظام كريستال دايموند
Excel Files Analysis - Crestal Diamond System
"""

import pandas as pd
import os
import sys

def analyze_excel_files():
    """تحليل ملفات الإكسل الموجودة"""
    
    # مسار الملفات
    path = r'C:\Users\<USER>\OneDrive\Desktop\crestal diamond'
    
    print('🔍 === فحص ملفات الإكسل - نظام كريستال دايموند ===')
    print('=' * 60)
    
    # قائمة ملفات الإكسل
    excel_files = [
        'casting.xlsx',
        'diamond.xlsx', 
        'femininty customer .xlsx',
        'wix matrix 3d.xlsx',
        'تلميع.xlsx',
        'جرد كامل.xlsx',
        'خسية مبرد.xlsx',
        'مصاريف.xlsx'
    ]
    
    analysis_results = {}
    
    for file in excel_files:
        file_path = os.path.join(path, file)
        if os.path.exists(file_path):
            print(f'\n📊 تحليل ملف: {file}')
            print('-' * 40)
            try:
                # قراءة الملف
                df = pd.read_excel(file_path)
                
                # معلومات أساسية
                rows_count = len(df)
                cols_count = len(df.columns)
                columns = list(df.columns)
                
                print(f'📈 عدد الصفوف: {rows_count}')
                print(f'📋 عدد الأعمدة: {cols_count}')
                print(f'🏷️ أسماء الأعمدة:')
                for i, col in enumerate(columns, 1):
                    print(f'   {i}. {col}')
                
                # حفظ النتائج
                analysis_results[file] = {
                    'rows': rows_count,
                    'columns': cols_count,
                    'column_names': columns,
                    'data': df,
                    'status': 'success'
                }
                
                # عرض عينة من البيانات
                if rows_count > 0:
                    print('\n📄 عينة من البيانات (أول 3 صفوف):')
                    sample_data = df.head(3)
                    for idx, row in sample_data.iterrows():
                        print(f'\nالصف {idx + 1}:')
                        for col in columns:
                            value = row[col]
                            if pd.notna(value):
                                print(f'   {col}: {value}')
                else:
                    print('📭 الملف فارغ')
                    
            except Exception as e:
                print(f'❌ خطأ في قراءة الملف: {e}')
                analysis_results[file] = {
                    'status': 'error',
                    'error': str(e)
                }
        else:
            print(f'❌ الملف غير موجود: {file}')
            analysis_results[file] = {
                'status': 'not_found'
            }
    
    return analysis_results

def analyze_invoice_structure(results):
    """تحليل هيكل الفواتير بناءً على التوضيحات المقدمة"""
    
    print('\n' + '=' * 60)
    print('📋 تحليل هيكل الفواتير بناءً على التوضيحات:')
    print('=' * 60)
    
    # التوضيحات المقدمة
    explanations = {
        'البيان': 'شرح القطعة التي استلمها العميل (مثل: كولية بها الماس تركيب 16 حجر)',
        'طرفكم': 'أحجار من العميل',
        'طرفنا': 'أحجار من خزينة الشركة', 
        'بانيو': 'عملية الطلاء الأبيض بمادة الروديوم + دمغة عيار 18 (750)',
        'وزن الذهب': 'الوزن بالجرام (مثل: 3.12)',
        'رصيد': 'المصنعية = وزن الذهب × 10$',
        'الرصيد المصري': 'عدد تركيب الأحجار × 5 جنيه (متغير)',
        'الدمغة': '20 جنيه أو أكثر',
        'البانيو': 'تكلفة إضافية للطلاء'
    }
    
    print('\n🔍 شرح المصطلحات:')
    for term, explanation in explanations.items():
        print(f'   • {term}: {explanation}')
    
    # البحث عن الملفات التي قد تحتوي على بيانات الفواتير
    invoice_files = []
    for file, data in results.items():
        if data.get('status') == 'success':
            columns = data.get('column_names', [])
            # البحث عن أعمدة تشبه بيانات الفواتير
            invoice_keywords = ['بيان', 'وزن', 'رصيد', 'عميل', 'ذهب', 'حجر', 'مصنعية']
            if any(keyword in str(col).lower() for col in columns for keyword in invoice_keywords):
                invoice_files.append(file)
    
    print(f'\n📊 الملفات التي قد تحتوي على بيانات فواتير: {invoice_files}')
    
    return invoice_files

def main():
    """الدالة الرئيسية"""
    try:
        # تحليل الملفات
        results = analyze_excel_files()
        
        # تحليل هيكل الفواتير
        invoice_files = analyze_invoice_structure(results)
        
        print('\n' + '=' * 60)
        print('📊 ملخص التحليل:')
        print('=' * 60)
        
        successful_files = [f for f, data in results.items() if data.get('status') == 'success']
        error_files = [f for f, data in results.items() if data.get('status') == 'error']
        missing_files = [f for f, data in results.items() if data.get('status') == 'not_found']
        
        print(f'✅ ملفات تم تحليلها بنجاح: {len(successful_files)}')
        print(f'❌ ملفات بها أخطاء: {len(error_files)}')
        print(f'📭 ملفات غير موجودة: {len(missing_files)}')
        print(f'🧾 ملفات محتملة للفواتير: {len(invoice_files)}')
        
        if successful_files:
            print('\n📋 الملفات الناجحة:')
            for file in successful_files:
                data = results[file]
                print(f'   • {file}: {data["rows"]} صف، {data["columns"]} عمود')
        
        print('\n💡 التوصيات:')
        print('   1. فحص الملفات المحتملة للفواتير بتفصيل أكثر')
        print('   2. تحديد هيكل الفاتورة المطلوب للنظام الجديد')
        print('   3. إنشاء نموذج فاتورة يتوافق مع النظام الحالي')
        
    except Exception as e:
        print(f'❌ خطأ عام في التحليل: {e}')

if __name__ == "__main__":
    main()
