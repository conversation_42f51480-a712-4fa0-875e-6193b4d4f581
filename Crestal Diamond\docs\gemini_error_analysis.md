# 🤖 طلب مساعدة Gemini CLI - تحليل أخطاء النظام

## 📋 تقرير الأخطاء المكتشفة

### 🚨 الأخطاء الحرجة:

#### 1. خطأ قاعدة البيانات - عمود غير موجود
```
ERROR: Unknown column 'customer_name' in 'where clause'
ERROR: Unknown column 'customer_name' in 'field list'
```
**الملفات المتأثرة**: `database/database_operations.py`
**السبب**: عدم تطابق بين هيكل الجدول والاستعلامات

#### 2. خطأ متغير غير معرف
```
NameError: name 'GOLD_FILE_PATH' is not defined
```
**الملف المتأثر**: `pages/02_gold_inventory.py:54`
**السبب**: متغير GOLD_FILE_PATH غير معرف في الكود

#### 3. خطأ مكتبة مفقودة
```
ModuleNotFoundError: No module named 'plotly'
```
**الملف المتأثر**: `pages/03_database_agent.py:11`
**السبب**: مكتبة plotly غير مثبتة

### 🎯 طلب التحليل من Gemini

**يا Gemini CLI، أحتاج مساعدتك في:**

1. **تحليل هيكل قاعدة البيانات**:
   - فحص جدول `transactions` والتأكد من الأعمدة الموجودة
   - اقتراح حلول لمشكلة `customer_name` vs `customer_id`
   - تحديد أفضل طريقة لربط الجداول

2. **إصلاح ملف مخزون الذهب**:
   - تحديد المتغيرات المطلوبة
   - اقتراح بديل لـ GOLD_FILE_PATH
   - تحويل النظام من CSV إلى MySQL

3. **حل مشاكل المكتبات**:
   - التأكد من تثبيت جميع المكتبات المطلوبة
   - اقتراح بدائل إذا لزم الأمر

4. **إنشاء بيانات تجريبية**:
   - بيانات عملاء تجريبية
   - فواتير تجريبية
   - معاملات مالية تجريبية
   - مخزون تجريبي

### 🔧 الحلول المطلوبة:

1. **إصلاح فوري للأخطاء الحرجة**
2. **خطة تدريجية للتحسين**
3. **اختبارات شاملة للتأكد من الإصلاح**
4. **توثيق الحلول المطبقة**

### 📊 معلومات النظام:
- **النظام**: كريستال دايموند - إدارة ورشة المجوهرات
- **التقنيات**: Python 3.13, Streamlit, MySQL, Pandas
- **قاعدة البيانات**: MySQL 8.0
- **الجداول**: customers, invoices, inventory, transactions

### 🎯 الهدف:
الحصول على نظام مستقر وخالي من الأخطاء مع بيانات تجريبية للاختبار.
