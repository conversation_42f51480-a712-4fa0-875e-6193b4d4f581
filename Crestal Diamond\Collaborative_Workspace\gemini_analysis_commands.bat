@echo off
REM 🤖 أوامر Gemini CLI لتحليل مشروع كريستال دايموند
REM Gemini CLI Commands for Crestal Diamond Project Analysis

echo ========================================
echo 🤖 Gemini CLI Analysis - Crestal Diamond
echo ========================================

REM تحليل هيكل المشروع
echo.
echo 📁 تحليل هيكل المشروع...
echo Project Structure Analysis...

REM عدد الملفات والمجلدات
echo.
echo 📊 إحصائيات المشروع:
dir /s /b *.py | find /c ".py"
echo Python files found

dir /s /b *.md | find /c ".md"
echo Markdown files found

dir /s /b *.json | find /c ".json"
echo JSON files found

dir /s /b *.csv | find /c ".csv"
echo CSV files found

REM حجم المشروع
echo.
echo 📏 حجم المشروع:
for /f "tokens=3" %%a in ('dir /-c ^| find "File(s)"') do echo Total size: %%a bytes

REM تحليل ملفات Python الرئيسية
echo.
echo 🐍 تحليل ملفات Python الرئيسية:
echo.

REM فحص app.py
if exist app.py (
    echo ✅ app.py موجود
    for /f %%i in ('find /c /v "" ^< app.py') do echo    - عدد الأسطر: %%i
) else (
    echo ❌ app.py غير موجود
)

REM فحص ملفات الصفحات
echo.
echo 📄 فحص ملفات الصفحات:
if exist pages\ (
    echo ✅ مجلد pages موجود
    dir pages\*.py /b
) else (
    echo ❌ مجلد pages غير موجود
)

REM فحص ملفات قاعدة البيانات
echo.
echo 🗄️ فحص ملفات قاعدة البيانات:
if exist database\ (
    echo ✅ مجلد database موجود
    dir database\*.py /b
) else (
    echo ❌ مجلد database غير موجود
)

REM فحص ملفات الوكلاء
echo.
echo 🤖 فحص ملفات الوكلاء:
if exist agents\ (
    echo ✅ مجلد agents موجود
    dir agents\*.py /b
) else (
    echo ❌ مجلد agents غير موجود
)

REM فحص requirements.txt
echo.
echo 📦 فحص التبعيات:
if exist requirements.txt (
    echo ✅ requirements.txt موجود
    for /f %%i in ('find /c /v "" ^< requirements.txt') do echo    - عدد التبعيات: %%i
    echo.
    echo 📋 التبعيات الرئيسية:
    findstr /i "streamlit pandas mysql" requirements.txt
) else (
    echo ❌ requirements.txt غير موجود
)

REM فحص README
echo.
echo 📖 فحص الوثائق:
if exist README.md (
    echo ✅ README.md موجود
    for /f %%i in ('find /c /v "" ^< README.md') do echo    - عدد الأسطر: %%i
) else (
    echo ❌ README.md غير موجود
)

REM فحص Collaborative_Workspace
echo.
echo 🤝 فحص مساحة العمل التعاونية:
if exist Collaborative_Workspace\ (
    echo ✅ Collaborative_Workspace موجود
    for /f %%i in ('dir Collaborative_Workspace\*.md /b ^| find /c ".md"') do echo    - ملفات التعاون: %%i
) else (
    echo ❌ Collaborative_Workspace غير موجود
)

REM تحليل الأداء
echo.
echo ⚡ تحليل الأداء:
echo.

REM فحص استخدام الذاكرة (تقريبي)
echo 💾 استخدام الذاكرة:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "="

REM فحص مساحة القرص
echo.
echo 💿 مساحة القرص:
for /f "tokens=3,4" %%a in ('dir /-c') do (
    if "%%a"=="bytes" echo المساحة المستخدمة: %%b bytes
)

REM تحليل جودة الكود
echo.
echo 🔍 تحليل جودة الكود:
echo.

REM فحص ملفات Python للأخطاء النحوية
echo 🐍 فحص الأخطاء النحوية:
python -m py_compile app.py 2>nul && echo ✅ app.py - لا توجد أخطاء نحوية || echo ❌ app.py - يحتوي على أخطاء

if exist pages\invoice.py (
    python -m py_compile pages\invoice.py 2>nul && echo ✅ pages\invoice.py - لا توجد أخطاء نحوية || echo ❌ pages\invoice.py - يحتوي على أخطاء
)

if exist database\database_config.py (
    python -m py_compile database\database_config.py 2>nul && echo ✅ database\database_config.py - لا توجد أخطاء نحوية || echo ❌ database\database_config.py - يحتوي على أخطاء
)

REM فحص التبعيات
echo.
echo 📦 فحص التبعيات المثبتة:
pip list | findstr /i "streamlit pandas mysql"

REM تحليل الأمان
echo.
echo 🔒 تحليل الأمان:
echo.

REM البحث عن كلمات مرور مكشوفة
echo 🔑 فحص كلمات المرور المكشوفة:
findstr /s /i "password pass secret key token" *.py *.json *.txt 2>nul | find /c ":" >nul && echo ⚠️ تم العثور على كلمات مرور محتملة || echo ✅ لم يتم العثور على كلمات مرور مكشوفة

REM البحث عن عناوين IP مكشوفة
echo.
echo 🌐 فحص عناوين IP:
findstr /s /r "[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*" *.py *.json *.txt 2>nul | find /c ":" >nul && echo ⚠️ تم العثور على عناوين IP || echo ✅ لم يتم العثور على عناوين IP مكشوفة

REM تحليل التنظيم
echo.
echo 📁 تحليل التنظيم:
echo.

REM فحص الملفات المؤقتة
echo 🗑️ فحص الملفات المؤقتة:
dir /s *.tmp *.log *.pyc 2>nul | find /c "File(s)" >nul && echo ⚠️ توجد ملفات مؤقتة تحتاج تنظيف || echo ✅ لا توجد ملفات مؤقتة

REM فحص الملفات المكررة
echo.
echo 📋 فحص الملفات المكررة:
dir /s README.md 2>nul | find /c "README.md" >nul && (
    for /f %%i in ('dir /s README.md ^| find /c "README.md"') do (
        if %%i GTR 1 echo ⚠️ توجد %%i ملفات README مكررة
        if %%i EQU 1 echo ✅ ملف README واحد فقط
    )
) || echo ❌ لا يوجد ملف README

REM إنشاء تقرير نهائي
echo.
echo ========================================
echo 📊 ملخص التحليل النهائي
echo ========================================

echo.
echo 📈 الإحصائيات:
for /f %%i in ('dir /s /b *.py ^| find /c ".py"') do echo - ملفات Python: %%i
for /f %%i in ('dir /s /b *.md ^| find /c ".md"') do echo - ملفات Markdown: %%i
for /f %%i in ('dir /s /b *.json ^| find /c ".json"') do echo - ملفات JSON: %%i

echo.
echo 🎯 التوصيات:
echo - تشغيل وكلاء التنظيم لترتيب الملفات
echo - تحديث ملفات README والوثائق
echo - فحص وتنظيف الملفات المؤقتة
echo - مراجعة أمان قاعدة البيانات

echo.
echo ✅ تم إكمال التحليل بنجاح!
echo 📅 التاريخ: %date% %time%

REM حفظ التقرير
echo.
echo 💾 حفظ التقرير...
echo Gemini CLI Analysis Report > Collaborative_Workspace\gemini_analysis_output.txt
echo Generated on: %date% %time% >> Collaborative_Workspace\gemini_analysis_output.txt
echo ======================================== >> Collaborative_Workspace\gemini_analysis_output.txt

echo.
echo 🎉 تم حفظ التقرير في: Collaborative_Workspace\gemini_analysis_output.txt

pause
