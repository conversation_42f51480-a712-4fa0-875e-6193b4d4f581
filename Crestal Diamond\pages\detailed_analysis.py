#!/usr/bin/env python3
"""
📊 تحليل مفصل لملف العملاء - نظام كريستال دايموند
Detailed Analysis of Customer File - Crestal Diamond System
"""

import pandas as pd
import os

def analyze_customer_file():
    """تحليل مفصل لملف العملاء الرئيسي"""
    
    file_path = r'C:\Users\<USER>\OneDrive\Desktop\crestal diamond\femininty customer .xlsx'
    
    print('📊 === تحليل مفصل لملف العملاء ===')
    print('=' * 50)
    
    try:
        # قراءة الملف
        df = pd.read_excel(file_path)
        
        print(f'📈 إجمالي الصفوف: {len(df)}')
        print(f'📋 إجمالي الأعمدة: {len(df.columns)}')
        
        # تحليل البيانات المهمة
        print('\n🔍 تحليل البيانات المهمة:')
        
        # البحث عن الأعمدة المهمة بناءً على التوضيحات
        important_columns = {}
        
        for idx, col in enumerate(df.columns):
            col_data = df[col].dropna()
            if len(col_data) > 0:
                sample_values = col_data.head(10).tolist()
                print(f'\n📋 العمود {idx}: {col}')
                print(f'   📊 عدد القيم غير الفارغة: {len(col_data)}')
                print(f'   📄 عينة من القيم: {sample_values[:5]}')
                
                # تحديد نوع البيانات
                if col_data.dtype == 'object':
                    # البحث عن كلمات مفتاحية
                    text_sample = ' '.join(str(v) for v in sample_values[:5])
                    keywords = ['بيان', 'ذهب', 'الماس', 'حجر', 'طرفكم', 'طرفنا', 'بانيو', 'دمغه', 'كولية', 'تركيب']
                    found_keywords = [k for k in keywords if k in text_sample]
                    if found_keywords:
                        print(f'   🔑 كلمات مفتاحية موجودة: {found_keywords}')
                        important_columns[f'col_{idx}'] = {
                            'name': col,
                            'type': 'description',
                            'keywords': found_keywords
                        }
                elif pd.api.types.is_numeric_dtype(col_data):
                    # أعمدة رقمية
                    print(f'   📊 نوع البيانات: رقمي')
                    print(f'   📈 المتوسط: {col_data.mean():.2f}')
                    print(f'   📉 الحد الأدنى: {col_data.min()}')
                    print(f'   📈 الحد الأقصى: {col_data.max()}')
                    
                    # تحديد نوع العمود الرقمي
                    if col_data.max() < 100 and col_data.min() >= 0:
                        important_columns[f'col_{idx}'] = {
                            'name': col,
                            'type': 'weight_or_count',
                            'stats': {'mean': col_data.mean(), 'min': col_data.min(), 'max': col_data.max()}
                        }
                    elif col_data.max() > 100:
                        important_columns[f'col_{idx}'] = {
                            'name': col,
                            'type': 'price_or_amount',
                            'stats': {'mean': col_data.mean(), 'min': col_data.min(), 'max': col_data.max()}
                        }
        
        # تحليل الصفوف المهمة
        print('\n📄 تحليل عينة من الصفوف المهمة:')
        
        # البحث عن صفوف تحتوي على بيانات فواتير
        for idx in range(min(10, len(df))):
            row = df.iloc[idx]
            non_null_values = row.dropna()
            
            if len(non_null_values) > 3:  # صف يحتوي على بيانات
                print(f'\n📋 الصف {idx + 1}:')
                for col_name, value in non_null_values.items():
                    if pd.notna(value) and str(value).strip():
                        print(f'   {col_name}: {value}')
        
        # تحليل الأنماط
        print('\n🔍 تحليل الأنماط المكتشفة:')
        
        # البحث عن أعمدة البيان
        description_columns = []
        for col in df.columns:
            col_data = df[col].dropna().astype(str)
            if any('ذهب' in str(v) or 'الماس' in str(v) or 'حجر' in str(v) for v in col_data.head(20)):
                description_columns.append(col)
        
        print(f'📝 أعمدة البيان المحتملة: {description_columns}')
        
        # البحث عن أعمدة الأوزان والأسعار
        numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
        print(f'🔢 الأعمدة الرقمية: {len(numeric_columns)} عمود')
        
        # البحث عن أعمدة التواريخ
        date_columns = []
        for col in df.columns:
            try:
                col_data = df[col].dropna()
                if len(col_data) > 0:
                    # محاولة تحويل لتاريخ
                    pd.to_datetime(col_data.head(5), errors='raise')
                    date_columns.append(col)
            except:
                pass
        
        print(f'📅 أعمدة التواريخ المحتملة: {date_columns}')
        
        return {
            'dataframe': df,
            'important_columns': important_columns,
            'description_columns': description_columns,
            'numeric_columns': numeric_columns,
            'date_columns': date_columns
        }
        
    except Exception as e:
        print(f'❌ خطأ في تحليل الملف: {e}')
        return None

def create_invoice_template(analysis_result):
    """إنشاء نموذج فاتورة بناءً على التحليل"""
    
    print('\n' + '=' * 50)
    print('📋 نموذج الفاتورة المقترح بناءً على التحليل:')
    print('=' * 50)
    
    # نموذج الفاتورة بناءً على التوضيحات
    invoice_template = {
        'معلومات أساسية': {
            'رقم الفاتورة': 'تلقائي',
            'تاريخ الفاتورة': 'تاريخ اليوم',
            'اسم العميل': 'من قائمة العملاء'
        },
        'تفاصيل القطعة': {
            'البيان': 'وصف القطعة (مثل: كولية ذهب أبيض تركيب 16 حجر الماس)',
            'وزن الذهب': 'بالجرام (مثل: 3.12)',
            'العيار': '18، 21، أو 24'
        },
        'تفاصيل الأحجار': {
            'عدد الأحجار': 'عدد الأحجار المركبة',
            'وزن الأحجار': 'بالقيراط (مثل: 0.08)',
            'مصدر الأحجار': 'طرفكم (العميل) أو طرفنا (الشركة)'
        },
        'الخدمات الإضافية': {
            'البانيو': 'طلاء الروديوم الأبيض',
            'الدمغة': 'دمغة العيار (750 للعيار 18)',
            'خدمات أخرى': 'حسب الحاجة'
        },
        'الحسابات المالية': {
            'المصنعية (دولار)': 'وزن الذهب × 10$',
            'تركيب الأحجار (جنيه)': 'عدد الأحجار × 5 جنيه (متغير)',
            'الدمغة (جنيه)': '20 جنيه أو أكثر',
            'البانيو (جنيه)': 'حسب الحجم والتعقيد',
            'الإجمالي بالدولار': 'المصنعية',
            'الإجمالي بالجنيه': 'تركيب + دمغة + بانيو + خدمات'
        }
    }
    
    for section, fields in invoice_template.items():
        print(f'\n📋 {section}:')
        for field, description in fields.items():
            print(f'   • {field}: {description}')
    
    return invoice_template

def main():
    """الدالة الرئيسية"""
    
    # تحليل ملف العملاء
    analysis_result = analyze_customer_file()
    
    if analysis_result:
        # إنشاء نموذج الفاتورة
        invoice_template = create_invoice_template(analysis_result)
        
        print('\n' + '=' * 50)
        print('💡 التوصيات للنظام الجديد:')
        print('=' * 50)
        
        recommendations = [
            'إنشاء نموذج فاتورة يتوافق مع النظام الحالي',
            'تطوير حاسبة تلقائية للمصنعية والخدمات',
            'إضافة قاعدة بيانات للعملاء مع تاريخ المعاملات',
            'تطوير نظام لتتبع المخزون (ذهب وأحجار)',
            'إنشاء تقارير مالية شاملة',
            'إضافة نظام للنسخ الاحتياطية التلقائية'
        ]
        
        for i, rec in enumerate(recommendations, 1):
            print(f'   {i}. {rec}')
        
        print('\n🎯 الخطوات التالية:')
        print('   1. مراجعة نموذج الفاتورة المقترح')
        print('   2. تحديد المعادلات الحسابية الدقيقة')
        print('   3. تطوير واجهة إدخال الفواتير')
        print('   4. اختبار النظام مع بيانات حقيقية')

if __name__ == "__main__":
    main()
