#!/usr/bin/env python3
"""
📁 وكيل تنظيم الملفات الذكي - نظام كريستال دايموند
Intelligent File Organizer Agent - Crestal Diamond System
يعمل مع Collaborative_Workspace و Gemini CLI و Llama3
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

class FileOrganizerAgent:
    """وكيل ذكي مسؤول عن تنظيم وترتيب الملفات"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.agent_name = "File Organizer Agent"
        self.log_file = self.project_root / "agents" / "file_organizer.log"
        
        # إنشاء مجلد الوكلاء
        (self.project_root / "agents").mkdir(exist_ok=True)
        
        # قواعد التنظيم
        self.organization_rules = {
            "core_files": {
                "target_dir": "",
                "files": ["app.py", "requirements.txt", "README.md", "run_app.bat"],
                "description": "الملفات الأساسية للمشروع"
            },
            "pages": {
                "target_dir": "pages",
                "patterns": ["*page*.py", "*invoice*.py", "*customer*.py", "*inventory*.py"],
                "extensions": [".py"],
                "exclude": ["__init__.py"],
                "description": "صفحات واجهة المستخدم"
            },
            "database": {
                "target_dir": "database",
                "patterns": ["*database*.py", "*db*.py", "*sql*", "*migration*.py"],
                "extensions": [".py", ".sql"],
                "description": "ملفات قاعدة البيانات"
            },
            "agents": {
                "target_dir": "agents",
                "patterns": ["*agent*.py", "*intelligent*.py"],
                "extensions": [".py", ".json", ".log"],
                "description": "الوكلاء الأذكياء"
            },
            "collaborative_workspace": {
                "target_dir": "Collaborative_Workspace",
                "patterns": ["*gemini*.md", "*llama*.md", "*collaboration*.md", "*team*.md"],
                "extensions": [".md", ".bat"],
                "description": "مساحة العمل التعاونية"
            },
            "documentation": {
                "target_dir": "docs",
                "patterns": ["*report*.md", "*analysis*.md", "*README*.md"],
                "extensions": [".md", ".txt", ".pdf"],
                "exclude": ["README.md"],  # الملف الرئيسي يبقى في الجذر
                "description": "الوثائق والتقارير"
            },
            "archive": {
                "target_dir": "Archive",
                "patterns": ["*old*", "*backup*", "*archive*", "*csv*"],
                "extensions": ["*"],
                "description": "الملفات المؤرشفة"
            },
            "temp_files": {
                "target_dir": "temp",
                "patterns": ["*temp*", "*tmp*"],
                "extensions": [".tmp", ".log", ".pyc", ".cache"],
                "description": "الملفات المؤقتة"
            }
        }
        
        self.log_action("تم تهيئة وكيل تنظيم الملفات")
    
    def log_action(self, action, details=""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {self.agent_name}: {action}"
        if details:
            log_entry += f" - {details}"
        
        print(f"📁 {log_entry}")
        
        # كتابة في ملف السجل
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
    
    def scan_project_files(self):
        """فحص جميع ملفات المشروع"""
        self.log_action("بدء فحص ملفات المشروع")
        
        all_files = []
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات النظام
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'crestal_diamond_env']]
            
            for file in files:
                if not file.startswith('.') and not file.endswith('.pyc'):
                    file_path = Path(root) / file
                    rel_path = file_path.relative_to(self.project_root)
                    
                    all_files.append({
                        'path': file_path,
                        'relative_path': rel_path,
                        'name': file,
                        'extension': file_path.suffix.lower(),
                        'size': file_path.stat().st_size if file_path.exists() else 0,
                        'current_dir': file_path.parent.name
                    })
        
        self.log_action("تم فحص الملفات", f"وجد {len(all_files)} ملف")
        return all_files
    
    def categorize_file(self, file_info):
        """تصنيف الملف حسب قواعد التنظيم"""
        file_name = file_info['name'].lower()
        file_ext = file_info['extension']
        current_dir = file_info['current_dir'].lower()
        
        # فحص كل فئة
        for category, rules in self.organization_rules.items():
            # فحص الملفات المحددة
            if 'files' in rules and file_info['name'] in rules['files']:
                return category
            
            # فحص الأنماط
            if 'patterns' in rules:
                for pattern in rules['patterns']:
                    pattern_clean = pattern.replace('*', '').lower()
                    if pattern_clean in file_name:
                        return category
            
            # فحص الامتدادات
            if 'extensions' in rules:
                if file_ext in rules['extensions'] or '*' in rules['extensions']:
                    # فحص الاستثناءات
                    if 'exclude' in rules and file_info['name'] in rules['exclude']:
                        continue
                    return category
            
            # فحص المجلد الحالي
            if rules['target_dir'].lower() == current_dir:
                return category
        
        return 'uncategorized'
    
    def create_directory_structure(self):
        """إنشاء هيكل المجلدات المطلوب"""
        self.log_action("إنشاء هيكل المجلدات")
        
        created_dirs = []
        for category, rules in self.organization_rules.items():
            if rules['target_dir']:  # تجاهل المجلد الجذر
                dir_path = self.project_root / rules['target_dir']
                if not dir_path.exists():
                    dir_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(str(dir_path))
                    self.log_action("تم إنشاء مجلد", rules['target_dir'])
        
        # إنشاء مجلد docs إذا لم يكن موجوداً
        docs_dir = self.project_root / "docs"
        if not docs_dir.exists():
            docs_dir.mkdir(exist_ok=True)
            created_dirs.append(str(docs_dir))
            self.log_action("تم إنشاء مجلد", "docs")
        
        return created_dirs
    
    def move_file_to_category(self, file_info, category, dry_run=True):
        """نقل ملف إلى الفئة المناسبة"""
        if category == 'uncategorized' or category not in self.organization_rules:
            return False, "فئة غير معروفة"
        
        source_path = file_info['path']
        target_dir = self.project_root / self.organization_rules[category]['target_dir']
        target_path = target_dir / file_info['name']
        
        # تجنب نقل الملف إلى نفس المكان
        if source_path.parent == target_dir:
            return True, "الملف في مكانه الصحيح"
        
        if dry_run:
            return True, f"سيتم نقل إلى {target_path}"
        
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # التعامل مع الملفات المكررة
            if target_path.exists():
                backup_name = f"{target_path.stem}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}{target_path.suffix}"
                target_path = target_dir / backup_name
                self.log_action("تم إنشاء نسخة احتياطية", backup_name)
            
            # نقل الملف
            shutil.move(str(source_path), str(target_path))
            return True, f"تم النقل إلى {target_path}"
            
        except Exception as e:
            return False, f"خطأ: {e}"
    
    def organize_all_files(self, dry_run=True):
        """تنظيم جميع ملفات المشروع"""
        self.log_action("بدء تنظيم جميع الملفات", f"وضع المحاكاة: {dry_run}")
        
        # فحص الملفات
        all_files = self.scan_project_files()
        
        # إنشاء هيكل المجلدات
        if not dry_run:
            self.create_directory_structure()
        
        # تصنيف ونقل الملفات
        organization_results = {
            'total_files': len(all_files),
            'categorized': {},
            'moved_files': [],
            'failed_moves': [],
            'uncategorized_files': []
        }
        
        for file_info in all_files:
            category = self.categorize_file(file_info)
            
            if category not in organization_results['categorized']:
                organization_results['categorized'][category] = 0
            organization_results['categorized'][category] += 1
            
            if category == 'uncategorized':
                organization_results['uncategorized_files'].append(str(file_info['relative_path']))
                continue
            
            # نقل الملف
            success, message = self.move_file_to_category(file_info, category, dry_run)
            
            if success:
                organization_results['moved_files'].append({
                    'file': str(file_info['relative_path']),
                    'category': category,
                    'target': self.organization_rules[category]['target_dir'],
                    'message': message
                })
                if not dry_run:
                    self.log_action("تم نقل الملف", f"{file_info['name']} -> {category}")
            else:
                organization_results['failed_moves'].append({
                    'file': str(file_info['relative_path']),
                    'category': category,
                    'error': message
                })
                self.log_action("فشل نقل الملف", f"{file_info['name']}: {message}")
        
        self.log_action("انتهى التنظيم", 
                       f"نقل: {len(organization_results['moved_files'])}, "
                       f"فشل: {len(organization_results['failed_moves'])}, "
                       f"غير مصنف: {len(organization_results['uncategorized_files'])}")
        
        return organization_results
    
    def clean_empty_directories(self, dry_run=True):
        """حذف المجلدات الفارغة"""
        self.log_action("بدء حذف المجلدات الفارغة", f"وضع المحاكاة: {dry_run}")
        
        removed_dirs = []
        
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                
                # تجاهل مجلدات مهمة
                if dir_name in ['.git', '__pycache__', 'agents', 'crestal_diamond_env']:
                    continue
                
                try:
                    # التحقق من أن المجلد فارغ
                    if not any(dir_path.iterdir()):
                        if dry_run:
                            self.log_action("محاكاة حذف مجلد فارغ", str(dir_path))
                        else:
                            dir_path.rmdir()
                            self.log_action("تم حذف مجلد فارغ", str(dir_path))
                        removed_dirs.append(str(dir_path))
                        
                except Exception as e:
                    self.log_action("خطأ في حذف مجلد", f"{dir_path}: {e}")
        
        return removed_dirs
    
    def generate_organization_report(self):
        """إنشاء تقرير شامل عن تنظيم المشروع"""
        # تشغيل محاكاة التنظيم
        results = self.organize_all_files(dry_run=True)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "agent": self.agent_name,
            "project_root": str(self.project_root),
            "organization_rules": self.organization_rules,
            "results": results,
            "recommendations": []
        }
        
        # إضافة التوصيات
        if len(results['uncategorized_files']) > 0:
            report['recommendations'].append(f"يوجد {len(results['uncategorized_files'])} ملف غير مصنف يحتاج مراجعة")
        
        if len(results['failed_moves']) > 0:
            report['recommendations'].append(f"فشل نقل {len(results['failed_moves'])} ملف - يحتاج تدخل يدوي")
        
        if results['categorized'].get('temp_files', 0) > 10:
            report['recommendations'].append("يوجد عدد كبير من الملفات المؤقتة - يُنصح بحذفها")
        
        # حفظ التقرير
        report_file = self.project_root / "agents" / "organization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log_action("تم إنشاء تقرير التنظيم", str(report_file))
        
        return report

def main():
    """اختبار الوكيل"""
    project_root = Path(__file__).parent.parent
    agent = FileOrganizerAgent(project_root)
    
    print("📁 === وكيل تنظيم الملفات الذكي ===")
    print("=" * 50)
    
    # إنشاء تقرير
    report = agent.generate_organization_report()
    
    print(f"📊 إجمالي الملفات: {report['results']['total_files']}")
    print(f"📦 ملفات ستُنقل: {len(report['results']['moved_files'])}")
    print(f"❓ ملفات غير مصنفة: {len(report['results']['uncategorized_files'])}")
    
    print("\n📋 التصنيف:")
    for category, count in report['results']['categorized'].items():
        print(f"   {category}: {count} ملف")
    
    print("\n💡 التوصيات:")
    for rec in report['recommendations']:
        print(f"   • {rec}")

if __name__ == "__main__":
    main()
