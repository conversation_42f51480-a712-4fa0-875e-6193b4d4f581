#!/usr/bin/env python3
"""
🗂️ وكيل مسارات الملفات الذكي - نظام كريستال دايموند
Intelligent File Path Agent - Crestal Diamond System
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

class FilePathAgent:
    """وكيل ذكي مسؤول عن إدارة مسارات الملفات"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.log_file = self.project_root / "agents" / "file_path_agent.log"
        self.config_file = self.project_root / "agents" / "file_structure_config.json"
        
        # إنشاء مجلد الوكلاء إذا لم يكن موجوداً
        (self.project_root / "agents").mkdir(exist_ok=True)
        
        # تحميل أو إنشاء إعدادات الهيكل
        self.load_structure_config()
        
    def load_structure_config(self):
        """تحميل إعدادات هيكل المشروع"""
        default_structure = {
            "core_files": {
                "description": "الملفات الأساسية للمشروع",
                "path": "",
                "files": ["app.py", "requirements.txt", "README.md", "run_app.bat"]
            },
            "pages": {
                "description": "صفحات النظام",
                "path": "pages",
                "files": ["*.py"],
                "exclude": ["__pycache__"]
            },
            "database": {
                "description": "ملفات قاعدة البيانات",
                "path": "database",
                "files": ["*.py", "*.sql"],
                "exclude": ["__pycache__", "*.pyc"]
            },
            "agents": {
                "description": "الوكلاء الأذكياء",
                "path": "agents",
                "files": ["*.py", "*.json", "*.log"],
                "exclude": ["__pycache__"]
            },
            "archive": {
                "description": "الأرشيف والملفات القديمة",
                "path": "Archive",
                "files": ["*"],
                "exclude": []
            },
            "docs": {
                "description": "الوثائق والتقارير",
                "path": "docs",
                "files": ["*.md", "*.txt", "*.pdf"],
                "exclude": []
            },
            "temp": {
                "description": "الملفات المؤقتة",
                "path": "temp",
                "files": ["*.tmp", "*.temp", "*.log"],
                "exclude": []
            },
            "backup": {
                "description": "النسخ الاحتياطية",
                "path": "backup",
                "files": ["*.bak", "*.backup"],
                "exclude": []
            }
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.structure_config = json.load(f)
        else:
            self.structure_config = default_structure
            self.save_structure_config()
    
    def save_structure_config(self):
        """حفظ إعدادات هيكل المشروع"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.structure_config, f, ensure_ascii=False, indent=2)
    
    def log_action(self, action, details=""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {action}: {details}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
        
        print(f"📝 {action}: {details}")
    
    def analyze_current_structure(self):
        """تحليل الهيكل الحالي للمشروع"""
        self.log_action("بدء تحليل هيكل المشروع")
        
        current_structure = {}
        
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            
            rel_path = os.path.relpath(root, self.project_root)
            if rel_path == '.':
                rel_path = 'root'
            
            current_structure[rel_path] = {
                'directories': dirs.copy(),
                'files': files.copy(),
                'total_files': len(files),
                'python_files': len([f for f in files if f.endswith('.py')]),
                'doc_files': len([f for f in files if f.endswith(('.md', '.txt', '.pdf'))]),
                'other_files': len([f for f in files if not f.endswith(('.py', '.md', '.txt', '.pdf', '.pyc'))])
            }
        
        self.log_action("تم تحليل الهيكل", f"وجد {len(current_structure)} مجلد")
        return current_structure
    
    def get_file_category(self, file_path):
        """تحديد فئة الملف بناءً على اسمه ومساره"""
        file_path = Path(file_path)
        file_name = file_path.name
        parent_dir = file_path.parent.name
        
        # ملفات Python
        if file_name.endswith('.py'):
            if parent_dir == 'pages' or 'page' in file_name.lower():
                return 'pages'
            elif parent_dir == 'database' or 'database' in file_name.lower() or 'db' in file_name.lower():
                return 'database'
            elif 'agent' in file_name.lower() or parent_dir == 'agents':
                return 'agents'
            elif file_name in ['app.py', 'main.py']:
                return 'core_files'
            else:
                return 'pages'  # افتراضي للملفات Python
        
        # ملفات الوثائق
        elif file_name.endswith(('.md', '.txt', '.pdf')):
            if file_name.upper() == 'README.MD':
                return 'core_files'
            else:
                return 'docs'
        
        # ملفات الإعدادات
        elif file_name in ['requirements.txt', 'run_app.bat', 'setup.py']:
            return 'core_files'
        
        # ملفات مؤقتة
        elif file_name.endswith(('.tmp', '.temp', '.log', '.pyc')):
            return 'temp'
        
        # ملفات النسخ الاحتياطية
        elif file_name.endswith(('.bak', '.backup')):
            return 'backup'
        
        # ملفات الأرشيف
        elif 'archive' in str(file_path).lower() or 'old' in file_name.lower():
            return 'archive'
        
        # افتراضي
        else:
            return 'docs'
    
    def create_directory_structure(self):
        """إنشاء هيكل المجلدات المطلوب"""
        self.log_action("إنشاء هيكل المجلدات")
        
        created_dirs = []
        for category, config in self.structure_config.items():
            dir_path = self.project_root / config['path']
            if not dir_path.exists() and config['path']:
                dir_path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(str(dir_path))
                self.log_action("تم إنشاء مجلد", str(dir_path))
        
        return created_dirs
    
    def move_file_to_category(self, file_path, category, dry_run=False):
        """نقل ملف إلى الفئة المناسبة"""
        source_path = Path(file_path)
        
        if not source_path.exists():
            self.log_action("ملف غير موجود", str(source_path))
            return False
        
        if category not in self.structure_config:
            self.log_action("فئة غير معروفة", category)
            return False
        
        target_dir = self.project_root / self.structure_config[category]['path']
        target_path = target_dir / source_path.name
        
        # تجنب نقل الملف إلى نفس المكان
        if source_path.parent == target_dir:
            return True
        
        if dry_run:
            self.log_action("محاكاة نقل", f"{source_path} -> {target_path}")
            return True
        
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # نقل الملف
            shutil.move(str(source_path), str(target_path))
            self.log_action("تم نقل الملف", f"{source_path} -> {target_path}")
            return True
            
        except Exception as e:
            self.log_action("خطأ في نقل الملف", f"{source_path}: {e}")
            return False
    
    def organize_all_files(self, dry_run=False):
        """تنظيم جميع الملفات في المشروع"""
        self.log_action("بدء تنظيم جميع الملفات", f"محاكاة: {dry_run}")
        
        # إنشاء هيكل المجلدات
        self.create_directory_structure()
        
        moved_files = []
        skipped_files = []
        
        # البحث عن جميع الملفات
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            
            for file in files:
                file_path = Path(root) / file
                
                # تجاهل ملفات معينة
                if file.startswith('.') or file.endswith('.pyc'):
                    continue
                
                # تحديد الفئة
                category = self.get_file_category(file_path)
                
                # نقل الملف
                if self.move_file_to_category(file_path, category, dry_run):
                    moved_files.append((str(file_path), category))
                else:
                    skipped_files.append(str(file_path))
        
        self.log_action("انتهى التنظيم", f"تم نقل {len(moved_files)} ملف، تم تجاهل {len(skipped_files)} ملف")
        
        return {
            'moved_files': moved_files,
            'skipped_files': skipped_files,
            'total_moved': len(moved_files),
            'total_skipped': len(skipped_files)
        }
    
    def clean_empty_directories(self, dry_run=False):
        """حذف المجلدات الفارغة"""
        self.log_action("بدء حذف المجلدات الفارغة", f"محاكاة: {dry_run}")
        
        removed_dirs = []
        
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                
                # تجاهل مجلدات معينة
                if dir_name.startswith('.') or dir_name in ['__pycache__', 'agents']:
                    continue
                
                try:
                    # التحقق من أن المجلد فارغ
                    if not any(dir_path.iterdir()):
                        if dry_run:
                            self.log_action("محاكاة حذف مجلد فارغ", str(dir_path))
                        else:
                            dir_path.rmdir()
                            self.log_action("تم حذف مجلد فارغ", str(dir_path))
                        removed_dirs.append(str(dir_path))
                        
                except Exception as e:
                    self.log_action("خطأ في حذف مجلد", f"{dir_path}: {e}")
        
        return removed_dirs
    
    def generate_report(self):
        """إنشاء تقرير شامل عن حالة المشروع"""
        current_structure = self.analyze_current_structure()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'total_directories': len(current_structure),
            'structure_analysis': current_structure,
            'recommendations': []
        }
        
        # تحليل وتوصيات
        total_files = sum(info['total_files'] for info in current_structure.values())
        python_files = sum(info['python_files'] for info in current_structure.values())
        doc_files = sum(info['doc_files'] for info in current_structure.values())
        
        report['summary'] = {
            'total_files': total_files,
            'python_files': python_files,
            'documentation_files': doc_files,
            'other_files': total_files - python_files - doc_files
        }
        
        # توصيات
        if python_files > 10:
            report['recommendations'].append("يُنصح بتنظيم ملفات Python في مجلدات فرعية")
        
        if doc_files < 3:
            report['recommendations'].append("يُنصح بإضافة المزيد من الوثائق")
        
        return report

def main():
    """الدالة الرئيسية لاختبار الوكيل"""
    project_root = Path(__file__).parent.parent
    agent = FilePathAgent(project_root)
    
    print("🗂️ === وكيل مسارات الملفات الذكي ===")
    print("=" * 50)
    
    # تحليل الهيكل الحالي
    print("📊 تحليل الهيكل الحالي...")
    structure = agent.analyze_current_structure()
    
    # إنشاء تقرير
    print("📋 إنشاء التقرير...")
    report = agent.generate_report()
    
    print(f"📈 إجمالي الملفات: {report['summary']['total_files']}")
    print(f"🐍 ملفات Python: {report['summary']['python_files']}")
    print(f"📄 ملفات الوثائق: {report['summary']['documentation_files']}")
    
    # محاكاة التنظيم
    print("\n🔄 محاكاة تنظيم الملفات...")
    result = agent.organize_all_files(dry_run=True)
    
    print(f"📦 سيتم نقل {result['total_moved']} ملف")
    print(f"⏭️ سيتم تجاهل {result['total_skipped']} ملف")
    
    print("\n💡 التوصيات:")
    for rec in report['recommendations']:
        print(f"   • {rec}")

if __name__ == "__main__":
    main()
