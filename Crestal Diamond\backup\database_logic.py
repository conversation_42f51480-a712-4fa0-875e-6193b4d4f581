import sqlite3
from datetime import datetime

DATABASE_NAME = 'crestal_diamond.db'

def _get_db_connection():
    conn = sqlite3.connect(DATABASE_NAME)
    conn.row_factory = sqlite3.Row
    return conn

# --- دوال العملاء ---
def add_customer(name, phone):
    try:
        conn = _get_db_connection()
        conn.execute("INSERT INTO Customers (name, phone) VALUES (?, ?)", (name, phone))
        conn.commit()
        conn.close()
        return True, "تمت إضافة العميل بنجاح"
    except sqlite3.IntegrityError:
        return False, "هذا العميل موجود بالفعل."

def get_all_customers():
    conn = _get_db_connection()
    customers = conn.execute("SELECT customer_id, name, phone FROM Customers ORDER BY name ASC").fetchall()
    conn.close()
    return [dict(row) for row in customers]

# --- دوال الموردين ---
def add_supplier(name, contact_info):
    try:
        conn = _get_db_connection()
        conn.execute("INSERT INTO Suppliers (name, contact_info) VALUES (?, ?)", (name, contact_info))
        conn.commit()
        conn.close()
        return True, "تمت إضافة المورد بنجاح"
    except sqlite3.IntegrityError:
        return False, "هذا المورد موجود بالفعل."

def get_all_suppliers():
    conn = _get_db_connection()
    suppliers = conn.execute("SELECT supplier_id, name FROM Suppliers ORDER BY name ASC").fetchall()
    conn.close()
    return [dict(row) for row in suppliers]

# --- دوال الحركات المالية (فواتير، دفعات، أرصدة) ---
def add_transaction(party_id, party_type, trans_type, desc, gold, usd, egp):
    conn = _get_db_connection()
    conn.execute("""
        INSERT INTO Transactions (party_id, party_type, transaction_type, transaction_date, description, gold_change, usd_change, egp_change)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (party_id, party_type, trans_type, datetime.now().date(), desc, gold, usd, egp))
    conn.commit()
    conn.close()

def get_customer_transactions(customer_id):
    conn = _get_db_connection()
    transactions = conn.execute("""
        SELECT * FROM Transactions
        WHERE party_id = ? AND party_type = 'Customer'
        ORDER BY transaction_date DESC
    """, (customer_id,)).fetchall()
    conn.close()
    return [dict(row) for row in transactions]

# --- دوال المخزون ---
def add_gold_inventory(carat, weight, desc):
    conn = _get_db_connection()
    conn.execute("""
        INSERT INTO Inventory_Gold (transaction_date, description, carat, weight_change)
        VALUES (?, ?, ?, ?)
    """, (datetime.now().date(), desc, carat, weight))
    conn.commit()
    conn.close()

def get_gold_inventory_summary():
    conn = _get_db_connection()
    summary = conn.execute("SELECT carat, SUM(weight_change) as balance FROM Inventory_Gold GROUP BY carat").fetchall()
    conn.close()
    return [dict(row) for row in summary]

# --- (يمكن إضافة باقي دوال المنطق هنا بنفس الطريقة) ---