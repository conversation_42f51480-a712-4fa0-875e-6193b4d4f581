#!/usr/bin/env python3
"""
🧮 حاسبة الفواتير المحدثة - نظام كريستال دايموند
Updated Invoice Calculator - Crestal Diamond System
"""

def calculate_workmanship(gold_weight, rate_per_gram=10.0):
    """
    حساب المصنعية بالدولار
    المعادلة: وزن الذهب × معدل متغير (افتراضي 10 دولار)
    """
    return round(gold_weight * rate_per_gram, 2)

def calculate_stone_setting(stone_count, rate_per_stone=5.0):
    """
    حساب تركيب الأحجار بالجنيه المصري
    المعادلة: عدد الأحجار × معدل متغير (افتراضي 5 جنيه)
    """
    return round(stone_count * rate_per_stone, 2)

def calculate_stamp_price(gold_carat='18', base_price=20.0, custom_rates=None):
    """
    حساب سعر الدمغة بالجنيه المصري - متغير حسب العيار والعميل
    العيار 18 = 750، العيار 21 = 875، العيار 24 = 999
    """
    if custom_rates:
        return custom_rates.get(gold_carat, base_price)

    # معدلات افتراضية متغيرة
    stamp_rates = {
        '18': base_price,
        '21': base_price + 5,
        '24': base_price + 10
    }
    return stamp_rates.get(gold_carat, base_price)

def calculate_banio_price(gold_weight, base_rate=15.0, custom_rate=None):
    """
    حساب سعر البانيو (طلاء الروديوم) بالجنيه المصري - متغير
    يمكن تخصيص السعر حسب العميل أو نوع القطعة
    """
    if custom_rate:
        return custom_rate

    # حساب متغير حسب الوزن (افتراضي)
    if gold_weight <= 1.0:
        return base_rate
    elif gold_weight <= 3.0:
        return base_rate * 1.5
    elif gold_weight <= 5.0:
        return base_rate * 2.0
    else:
        return base_rate * 2.5

def calculate_total_usd(gold_weight, workmanship_rate=10.0, stone_price_usd=0):
    """
    حساب الإجمالي بالدولار
    """
    workmanship = calculate_workmanship(gold_weight, workmanship_rate)
    return round(workmanship + stone_price_usd, 2)

def calculate_total_egp(stone_count, stone_setting_rate=5.0, 
                       banio_service=False, banio_price=0,
                       stamp_service=False, stamp_price=20,
                       additional_services=0):
    """
    حساب الإجمالي بالجنيه المصري
    """
    total = 0
    
    # تركيب الأحجار
    total += calculate_stone_setting(stone_count, stone_setting_rate)
    
    # البانيو
    if banio_service:
        total += banio_price
    
    # الدمغة
    if stamp_service:
        total += stamp_price
    
    # خدمات إضافية
    total += additional_services
    
    return round(total, 2)

def get_invoice_template_data(gold_weight=0, stone_count=0, gold_carat='18',
                             stone_source='طرفكم', banio_service=True, 
                             stamp_service=True):
    """
    إنشاء نموذج فاتورة بالحسابات المحدثة
    """
    template = {
        # الحسابات الأساسية
        'workmanship_rate_usd': 10.0,
        'stone_setting_rate_egp': 5.0,
        
        # حسابات المصنعية
        'workmanship_total_usd': calculate_workmanship(gold_weight),
        
        # حسابات الأحجار
        'stone_setting_total_egp': calculate_stone_setting(stone_count),
        
        # حسابات الخدمات
        'stamp_price_egp': calculate_stamp_price(gold_carat) if stamp_service else 0,
        'banio_price_egp': calculate_banio_price(gold_weight) if banio_service else 0,
        
        # الإجماليات
        'subtotal_usd': calculate_workmanship(gold_weight),
        'subtotal_egp': (
            calculate_stone_setting(stone_count) +
            (calculate_stamp_price(gold_carat) if stamp_service else 0) +
            (calculate_banio_price(gold_weight) if banio_service else 0)
        ),
        
        # معلومات إضافية
        'gold_carat': gold_carat,
        'stone_source': stone_source,
        'banio_service': banio_service,
        'stamp_service': stamp_service,
        'order_status': 'جديد'
    }
    
    # حساب الإجماليات النهائية
    template['total_usd'] = template['subtotal_usd']
    template['total_egp'] = template['subtotal_egp']
    
    return template

def validate_invoice_data(invoice_data):
    """
    التحقق من صحة بيانات الفاتورة
    """
    errors = []
    
    # التحقق من الحقول المطلوبة
    required_fields = ['customer_name', 'main_product']
    for field in required_fields:
        if not invoice_data.get(field):
            errors.append(f"الحقل '{field}' مطلوب")
    
    # التحقق من القيم الرقمية
    numeric_fields = ['gold_weight', 'stone_count', 'workmanship_rate_usd', 'stone_setting_rate_egp']
    for field in numeric_fields:
        value = invoice_data.get(field, 0)
        if not isinstance(value, (int, float)) or value < 0:
            errors.append(f"الحقل '{field}' يجب أن يكون رقم موجب")
    
    # التحقق من العيار
    valid_carats = ['18', '21', '24']
    if invoice_data.get('gold_carat') not in valid_carats:
        errors.append("عيار الذهب يجب أن يكون 18 أو 21 أو 24")
    
    # التحقق من مصدر الأحجار
    valid_sources = ['طرفكم', 'طرفنا', 'مختلط']
    if invoice_data.get('stone_source') not in valid_sources:
        errors.append("مصدر الأحجار يجب أن يكون 'طرفكم' أو 'طرفنا' أو 'مختلط'")
    
    return errors

def format_invoice_description(main_product, gold_carat, stone_count, 
                              stone_source, banio_service, stamp_service):
    """
    تنسيق وصف الفاتورة بالطريقة المطابقة لملفات الإكسل
    """
    description = main_product
    
    if gold_carat:
        description += f" عيار {gold_carat}"
    
    if stone_count > 0:
        description += f" تركيب {stone_count} حجر"
        if stone_source:
            description += f" {stone_source}"
    
    services = []
    if banio_service:
        services.append("بانيو")
    if stamp_service:
        services.append("دمغة")
    
    if services:
        description += " + " + " + ".join(services)
    
    return description

def convert_excel_to_invoice_data(excel_row):
    """
    تحويل صف من ملف الإكسل إلى بيانات فاتورة
    """
    # استخراج البيانات من البيان
    description = excel_row.get('بيان', '')
    
    # تحليل البيان لاستخراج المعلومات
    invoice_data = {
        'main_product': description,
        'gold_weight': excel_row.get('دهب', 0),
        'workmanship_total_usd': excel_row.get('رصيد', 0),
        'stone_weight': excel_row.get('ماس', 0),
        'additional_services_total_egp': excel_row.get('مصري', 0),
        'invoice_date': excel_row.get('تاريخ'),
        
        # استخراج معلومات من البيان
        'banio_service': 'بانيو' in description,
        'stamp_service': 'دمغة' in description or 'دمغه' in description,
        'stone_source': 'طرفكم' if 'طرفكم' in description else 'طرفنا' if 'طرفنا' in description else 'طرفكم',
        
        # تحديد العيار (افتراضي 18)
        'gold_carat': '21' if 'عيار 21' in description else '24' if 'عيار 24' in description else '18',
        
        # حالة الطلب
        'order_status': 'مسلم' if 'TOTAL' in description else 'جديد'
    }
    
    return invoice_data

# دوال مساعدة للتحويل والتنسيق
def format_currency(amount, currency='EGP'):
    """تنسيق العملة"""
    if currency == 'USD':
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.2f} ج.م"

def get_carat_display_name(carat):
    """الحصول على اسم العيار للعرض"""
    carat_names = {
        '18': 'عيار 18 (750)',
        '21': 'عيار 21 (875)', 
        '24': 'عيار 24 (999)'
    }
    return carat_names.get(carat, f'عيار {carat}')

def get_stone_source_display_name(source):
    """الحصول على اسم مصدر الأحجار للعرض"""
    source_names = {
        'طرفكم': 'أحجار العميل',
        'طرفنا': 'أحجار الشركة',
        'مختلط': 'أحجار مختلطة'
    }
    return source_names.get(source, source)
