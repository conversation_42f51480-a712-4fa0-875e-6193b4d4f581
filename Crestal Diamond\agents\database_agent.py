#!/usr/bin/env python3
"""
🗄️ وكيل قاعدة البيانات الذكي - نظام كريستال دايموند
Intelligent Database Agent - Crestal Diamond System
يعمل مع Collaborative_Workspace و Gemini CLI و Llama3
"""

import os
import sys
import json
import mysql.connector
from datetime import datetime
from pathlib import Path

# إضافة مجلد قاعدة البيانات إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'database'))

try:
    from database_config import get_connection, test_connection
    from database_operations import customer_ops, invoice_ops, transaction_ops
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: لا يمكن تحميل وحدات قاعدة البيانات: {e}")
    DATABASE_AVAILABLE = False

class DatabaseAgent:
    """وكيل ذكي مسؤول عن إدارة وصيانة قاعدة البيانات"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.agent_name = "Database Agent"
        self.log_file = self.project_root / "agents" / "database_agent.log"
        
        # إنشاء مجلد الوكلاء
        (self.project_root / "agents").mkdir(exist_ok=True)
        
        # معلومات قاعدة البيانات
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'crestal_diamond'
        }
        
        # جداول النظام المطلوبة
        self.required_tables = [
            'customers',
            'invoices', 
            'transactions',
            'inventory',
            'invoice_templates'
        ]
        
        self.log_action("تم تهيئة وكيل قاعدة البيانات")
    
    def log_action(self, action, details=""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {self.agent_name}: {action}"
        if details:
            log_entry += f" - {details}"
        
        print(f"🗄️ {log_entry}")
        
        # كتابة في ملف السجل
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        self.log_action("اختبار الاتصال بقاعدة البيانات")
        
        if not DATABASE_AVAILABLE:
            self.log_action("فشل الاختبار", "وحدات قاعدة البيانات غير متوفرة")
            return False
        
        try:
            if test_connection():
                self.log_action("نجح الاتصال", "قاعدة البيانات متاحة")
                return True
            else:
                self.log_action("فشل الاتصال", "لا يمكن الوصول لقاعدة البيانات")
                return False
        except Exception as e:
            self.log_action("خطأ في الاتصال", str(e))
            return False
    
    def analyze_database_structure(self):
        """تحليل هيكل قاعدة البيانات"""
        self.log_action("بدء تحليل هيكل قاعدة البيانات")
        
        if not self.test_database_connection():
            return None
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'connection_status': 'connected',
            'tables': {},
            'missing_tables': [],
            'table_count': 0,
            'total_records': 0,
            'issues': [],
            'recommendations': []
        }
        
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SHOW TABLES")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            analysis['table_count'] = len(existing_tables)
            
            # فحص الجداول المطلوبة
            for table in self.required_tables:
                if table in existing_tables:
                    # تحليل الجدول
                    table_info = self.analyze_table(cursor, table)
                    analysis['tables'][table] = table_info
                    analysis['total_records'] += table_info['record_count']
                else:
                    analysis['missing_tables'].append(table)
                    analysis['issues'].append(f"الجدول {table} مفقود")
            
            # فحص الجداول الإضافية
            extra_tables = [t for t in existing_tables if t not in self.required_tables]
            for table in extra_tables:
                table_info = self.analyze_table(cursor, table)
                analysis['tables'][table] = table_info
                analysis['total_records'] += table_info['record_count']
            
            # إنشاء التوصيات
            if len(analysis['missing_tables']) > 0:
                analysis['recommendations'].append(f"إنشاء {len(analysis['missing_tables'])} جدول مفقود")
            
            if analysis['total_records'] == 0:
                analysis['recommendations'].append("إضافة بيانات تجريبية للاختبار")
            
            if analysis['table_count'] > 10:
                analysis['recommendations'].append("مراجعة الجداول الإضافية وحذف غير المستخدم")
            
            cursor.close()
            conn.close()
            
            self.log_action("تم تحليل قاعدة البيانات", 
                           f"الجداول: {analysis['table_count']}, السجلات: {analysis['total_records']}")
            
        except Exception as e:
            analysis['connection_status'] = 'error'
            analysis['issues'].append(f"خطأ في التحليل: {e}")
            self.log_action("خطأ في تحليل قاعدة البيانات", str(e))
        
        return analysis
    
    def analyze_table(self, cursor, table_name):
        """تحليل جدول محدد"""
        table_info = {
            'name': table_name,
            'record_count': 0,
            'columns': [],
            'indexes': [],
            'size_mb': 0,
            'last_update': None,
            'issues': []
        }
        
        try:
            # عدد السجلات
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            table_info['record_count'] = cursor.fetchone()[0]
            
            # معلومات الأعمدة
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            for col in columns:
                table_info['columns'].append({
                    'name': col[0],
                    'type': col[1],
                    'null': col[2],
                    'key': col[3],
                    'default': col[4],
                    'extra': col[5]
                })
            
            # معلومات الفهارس
            cursor.execute(f"SHOW INDEX FROM {table_name}")
            indexes = cursor.fetchall()
            for idx in indexes:
                if idx[2] not in [i['name'] for i in table_info['indexes']]:
                    table_info['indexes'].append({
                        'name': idx[2],
                        'column': idx[4],
                        'unique': not bool(idx[1])
                    })
            
            # حجم الجدول
            cursor.execute(f"""
                SELECT 
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE() AND table_name = '{table_name}'
            """)
            result = cursor.fetchone()
            if result and result[0]:
                table_info['size_mb'] = float(result[0])
            
            # فحص المشاكل
            if table_info['record_count'] == 0:
                table_info['issues'].append("الجدول فارغ")
            
            if len(table_info['indexes']) == 0:
                table_info['issues'].append("لا توجد فهارس")
            
        except Exception as e:
            table_info['issues'].append(f"خطأ في تحليل الجدول: {e}")
        
        return table_info
    
    def check_database_health(self):
        """فحص صحة قاعدة البيانات"""
        self.log_action("بدء فحص صحة قاعدة البيانات")
        
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'connection_test': False,
            'structure_analysis': None,
            'performance_metrics': {},
            'security_check': {},
            'backup_status': 'unknown',
            'recommendations': []
        }
        
        # اختبار الاتصال
        health_report['connection_test'] = self.test_database_connection()
        
        if health_report['connection_test']:
            # تحليل الهيكل
            health_report['structure_analysis'] = self.analyze_database_structure()
            
            # فحص الأداء
            health_report['performance_metrics'] = self.check_performance()
            
            # فحص الأمان
            health_report['security_check'] = self.check_security()
            
            # تحديد الحالة العامة
            if (health_report['structure_analysis'] and 
                len(health_report['structure_analysis']['missing_tables']) == 0):
                health_report['overall_status'] = 'healthy'
            else:
                health_report['overall_status'] = 'needs_attention'
        else:
            health_report['overall_status'] = 'critical'
            health_report['recommendations'].append("إصلاح مشكلة الاتصال بقاعدة البيانات")
        
        # حفظ التقرير
        report_file = self.project_root / "agents" / "database_health_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(health_report, f, ensure_ascii=False, indent=2)
        
        self.log_action("تم إنشاء تقرير صحة قاعدة البيانات", str(report_file))
        
        return health_report
    
    def check_performance(self):
        """فحص أداء قاعدة البيانات"""
        performance = {
            'query_time': 0,
            'connection_time': 0,
            'slow_queries': 0,
            'status': 'good'
        }
        
        try:
            start_time = datetime.now()
            conn = get_connection()
            connection_time = (datetime.now() - start_time).total_seconds()
            
            cursor = conn.cursor()
            
            # اختبار سرعة الاستعلام
            query_start = datetime.now()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            query_time = (datetime.now() - query_start).total_seconds()
            
            performance['connection_time'] = connection_time
            performance['query_time'] = query_time
            
            # تحديد الحالة
            if connection_time > 2 or query_time > 1:
                performance['status'] = 'slow'
            elif connection_time > 5 or query_time > 3:
                performance['status'] = 'critical'
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            performance['status'] = 'error'
            performance['error'] = str(e)
        
        return performance
    
    def check_security(self):
        """فحص أمان قاعدة البيانات"""
        security = {
            'password_protected': True,
            'remote_access': False,
            'encryption': False,
            'backup_encrypted': False,
            'status': 'secure'
        }
        
        # فحص أساسي للأمان
        if self.db_config['password'] == '':
            security['password_protected'] = False
            security['status'] = 'vulnerable'
        
        return security
    
    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        self.log_action("بدء إنشاء نسخة احتياطية")
        
        backup_dir = self.project_root / "database" / "backups"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_dir / f"crestal_diamond_backup_{timestamp}.sql"
        
        try:
            # أمر mysqldump (يحتاج MySQL Client)
            import subprocess
            
            cmd = [
                'mysqldump',
                '-h', self.db_config['host'],
                '-u', self.db_config['user'],
                f"-p{self.db_config['password']}" if self.db_config['password'] else '',
                self.db_config['database']
            ]
            
            with open(backup_file, 'w') as f:
                result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                self.log_action("تم إنشاء النسخة الاحتياطية", str(backup_file))
                return str(backup_file)
            else:
                self.log_action("فشل في إنشاء النسخة الاحتياطية", result.stderr)
                return None
                
        except Exception as e:
            self.log_action("خطأ في إنشاء النسخة الاحتياطية", str(e))
            return None
    
    def generate_database_report(self):
        """إنشاء تقرير شامل عن قاعدة البيانات"""
        self.log_action("إنشاء تقرير شامل عن قاعدة البيانات")
        
        # فحص صحة قاعدة البيانات
        health_report = self.check_database_health()
        
        # إنشاء تقرير نصي
        report_lines = [
            "🗄️ تقرير قاعدة البيانات - نظام كريستال دايموند",
            "=" * 60,
            f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"🤖 الوكيل: {self.agent_name}",
            "",
            "📊 الحالة العامة:",
            f"   🔗 الاتصال: {'✅ متصل' if health_report['connection_test'] else '❌ غير متصل'}",
            f"   🏥 الصحة: {health_report['overall_status']}",
            ""
        ]
        
        if health_report['structure_analysis']:
            analysis = health_report['structure_analysis']
            report_lines.extend([
                "📋 تحليل الهيكل:",
                f"   📊 عدد الجداول: {analysis['table_count']}",
                f"   📈 إجمالي السجلات: {analysis['total_records']}",
                f"   ❌ جداول مفقودة: {len(analysis['missing_tables'])}",
                ""
            ])
            
            if analysis['missing_tables']:
                report_lines.append("⚠️ الجداول المفقودة:")
                for table in analysis['missing_tables']:
                    report_lines.append(f"   - {table}")
                report_lines.append("")
        
        if health_report['recommendations']:
            report_lines.append("💡 التوصيات:")
            for rec in health_report['recommendations']:
                report_lines.append(f"   • {rec}")
            report_lines.append("")
        
        report_lines.extend([
            "=" * 60,
            "✅ تم إنشاء التقرير بواسطة وكيل قاعدة البيانات الذكي"
        ])
        
        # حفظ التقرير
        report_file = self.project_root / "agents" / "database_comprehensive_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        self.log_action("تم إنشاء التقرير الشامل", str(report_file))
        
        return report_file

def main():
    """اختبار الوكيل"""
    project_root = Path(__file__).parent.parent
    agent = DatabaseAgent(project_root)
    
    print("🗄️ === وكيل قاعدة البيانات الذكي ===")
    print("=" * 50)
    
    # إنشاء تقرير شامل
    report_file = agent.generate_database_report()
    
    print(f"📋 تم إنشاء التقرير: {report_file}")
    
    # عرض ملخص
    health = agent.check_database_health()
    print(f"🏥 حالة قاعدة البيانات: {health['overall_status']}")
    print(f"🔗 الاتصال: {'✅ يعمل' if health['connection_test'] else '❌ لا يعمل'}")

if __name__ == "__main__":
    main()
