{"core_files": {"description": "الملفات الأساسية للمشروع", "path": "", "files": ["app.py", "requirements.txt", "README.md", "run_app.bat"]}, "pages": {"description": "صفحات النظام", "path": "pages", "files": ["*.py"], "exclude": ["__pycache__"]}, "database": {"description": "ملفات قاعدة البيانات", "path": "database", "files": ["*.py", "*.sql"], "exclude": ["__pycache__", "*.pyc"]}, "agents": {"description": "الوكلاء الأذكياء", "path": "agents", "files": ["*.py", "*.json", "*.log"], "exclude": ["__pycache__"]}, "archive": {"description": "الأرشيف والملفات القديمة", "path": "Archive", "files": ["*"], "exclude": []}, "docs": {"description": "الوثائق والتقارير", "path": "docs", "files": ["*.md", "*.txt", "*.pdf"], "exclude": []}, "temp": {"description": "الملفات المؤقتة", "path": "temp", "files": ["*.tmp", "*.temp", "*.log"], "exclude": []}, "backup": {"description": "النسخ الاحتياطية", "path": "backup", "files": ["*.bak", "*.backup"], "exclude": []}}